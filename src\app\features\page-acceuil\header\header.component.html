<!-- Top Bar -->
<div class="top-bar" [class.hidden]="isScrolled">
  <div class="left">
    <span><mat-icon>phone</mat-icon>+212606060606</span>
    <span class="divider">|</span>
    <span><mat-icon>email</mat-icon>delegation&#64;gmail.com</span>
  </div>
  <div class="right">
    <a href="#"><mat-icon class="fab fa-facebook"></mat-icon></a>
    <a href="#"><mat-icon class="fab fa-x"></mat-icon></a>
    <a href="#"><mat-icon class="fab fa-instagram"></mat-icon></a>
    <a href="#"><mat-icon class="fab fa-youtube"></mat-icon></a>
  </div>
</div>

<!-- Main Bar -->
<div class="main-bar" [class.compact]="isScrolled">
  <div class="logo-container">
    <img src="assets/images/loogo.png" alt="Logo" />
    <div class="title">Délégation Santé</div>
  </div>

  <div class="center">
      <!-- Pour admin  - affichage normal -->
  <ng-container *ngIf="role === 'admin'">
    <a *ngFor="let link of navLinks"
       class="btn"
       [routerLink]="link.link"
       routerLinkActive="active">{{ link.label }}</a>
  </ng-container>
  <!-- Pour bénéficiaire - liens centrés -->
  <div *ngIf="role === 'beneficiaire'" class="beneficiary-links">
    <a *ngFor="let link of navLinks"
       class="btn"
       [routerLink]="link.link"
       routerLinkActive="active">{{ link.label }}</a>
  </div>
    <!-- Pour bénéficiaire - liens centrés -->
  <div *ngIf="role === 'public'" class="beneficiary-links">
    <a *ngFor="let link of navLinks"
       class="btn"
          [routerLink]="link.label !== 'Contact' ? link.link : null"
          (click)="link.label === 'Contact' ? scrollToFooter() : null"
       routerLinkActive="active">{{ link.label }}</a>
  </div>
  </div>

  <div class="right">
    <!-- Auth boutons (public uniquement) -->
    <ng-container *ngIf="showAuthButtons">
      <button class="login-btn" routerLink="/login">Se connecter</button>
      <button class="signup-btn" routerLink="/register">S'inscrire</button>
    </ng-container>

    <!-- Dropdown profil (admin et bénéficiaire) -->
    <!-- Profil Dropdown -->
    <!-- Simple bouton cercle profil -->
  <button *ngIf="role === 'admin' || role === 'beneficiaire'" 
          class="profile-circle-btn"
          (click)="toggleDropdown()">
    <mat-icon>account_circle</mat-icon>
  </button>
</div>

<!-- Menu dropdown séparé -->
<div class="profile-dropdown-menu" *ngIf="showProfileDropdown" @dropdownAnimation>
  <a routerLink="/infos"><mat-icon>person</mat-icon> Mes infos</a>

  <a [routerLink]="getChangePasswordLink()">
    <mat-icon>lock</mat-icon> Changer mot de passe
  </a>

  <a routerLink="/login"><mat-icon>logout</mat-icon> Se déconnecter</a>
</div>

</div>
