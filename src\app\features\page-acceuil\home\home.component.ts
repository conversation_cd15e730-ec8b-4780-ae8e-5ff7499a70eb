import { Component } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';

interface Service {
  id: number;
  title: string;
  image: string;
}

export interface Evenement {
  id: number;
  titre: string;
  beneficiaires: number;
  image: string;
  date?: Date;
  lieu?: string;
  description?: string;
}

@Component({
  selector: 'app-home',
  imports: [ MatFormFieldModule, MatInputModule, MatCardModule,
     MatIconModule, MatButtonModule,
      FormsModule, CommonModule ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent {

  searchQuery: string = '';

  onSearch() {
    if (this.searchQuery.trim()) {
      console.log('Recherche pour:', this.searchQuery);
    }
  }

  currentIndex = 0;
  servicesPerPage = 3;

  services: Service[] = [
    {
      id: 1,
      title: 'Lunettes',
      image: 'assets/images/lunettes.jpg'
    },
    {
      id: 2,
      title: 'Appareils auditives',
      image: 'assets/images/appareils-auditifs.jpg'
    },
    {
      id: 3,
      title: 'Canne à pied',
      image: 'assets/images/canne-pied.jpg'
    },
    {
      id: 4,
      title: 'Fauteuil roulant',
      image: 'assets/images/fauteuil-roulant.jpg'
    },
    {
      id: 5,
      title: 'Prothèses',
      image: 'assets/images/protheses.jpg'
    },
    {
      id: 6,
      title: 'Équipements médicaux',
      image: 'assets/images/canne-a-pied.jpg'
    }
  ];

  constructor(private router: Router) {
    this.updateCardsPerView();
    this.updateTranslateX();
  }

  get totalPages(): number {
    return Math.max(0, this.services.length - this.servicesPerPage + 1);
  }

  getCurrentServices(): Service[] {
    return this.services.slice(this.currentIndex, this.currentIndex + this.servicesPerPage);
  }

  nextPage(): void {
    if (this.currentIndex < this.services.length - this.servicesPerPage) {
      this.currentIndex++;
    }
  }

  previousPage(): void {
    if (this.currentIndex > 0) {
      this.currentIndex--;
    }
  }

  goToPage(pageIndex: number): void {
    this.currentIndex = pageIndex;
  }

  getPageIndicators(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i);
  }

  makeRequest(): void {
    this.router.navigate(['/beneficiaire/demandes']);
  }

  trackByServiceId(index: number, service: Service): number {
    return service.id;
  }

  evenements: Evenement[] = [
    {
      id: 1,
      titre: 'Dépistage du diabète',
      beneficiaires: 80,
      image: 'assets/images/dipestage.jpg',
      date: new Date('2023-12-15'),
      lieu: 'Hôpital Central',
      description: 'Campagne de dépistage gratuit du diabète pour tous les citoyens de plus de 40 ans.'
    },
    {
      id: 2,
      titre: 'Campagne de vaccination gratuite',
      beneficiaires: 200,
      image: 'assets/images/vaccinnation-rougeole.jpg',
      date: new Date('2023-11-20'),
      lieu: 'Centre de Santé Communautaire',
      description: 'Vaccination gratuite contre la rougeole pour les enfants de moins de 5 ans.'
    },
    {
      id: 3,
      titre: 'Dons de sang organisés',
      beneficiaires: 150,
      image: 'assets/images/donnate_sang.jpg',
      date: new Date('2023-10-10'),
      lieu: 'Stade Municipal',
      description: 'Collecte de dons de sang pour les patients en nécessité.'
    },
    {
      id: 4,
      titre: 'Consultations médicales gratuites',
      beneficiaires: 100,
      image: 'assets/images/consultation.jfif',
      date: new Date('2023-09-05'),
      lieu: 'Clinique du Centre',
      description: 'Consultations médicales gratuites pour les personnes âgées de 65 ans et plus.'
    },
    {
      id: 5,
      titre: 'Distribution de médicaments essentiels',
      beneficiaires: 130,
      image: 'assets/images/pharmacie.jpg',
      date: new Date('2023-08-15'),
      lieu: 'Pharmacie Centrale',
      description: 'Distribution gratuite de médicaments essentiels pour les personnes à faible revenu.'
    },
    {
      id: 6,
      titre: 'Soins dentaires pour enfants',
      beneficiaires: 70,
      image: 'assets/images/dentaires.jfif',
      date: new Date('2023-07-25'),
      lieu: 'Clinique Dentaire',
      description: 'Soins dentaires gratuits pour les enfants de moins de 12 ans.'
    },
    {
      id: 7,
      titre: 'Sensibilisation à la santé mentale',
      beneficiaires: 90,
      image: 'assets/images/sante_mentale.jpg',
      date: new Date('2023-06-30'),
      lieu: 'Centre de Santé Mentale',
      description: 'Séances de sensibilisation à la santé mentale pour les adolescents.'
    },
    {
      id: 8,
      titre: 'Rééducation pour personnes handicapées',
      beneficiaires: 65,
      image: 'assets/images/handicape.jpg',
      date: new Date('2023-05-15'),
      lieu: 'Centre de Rééducation',
      description: 'Programme de rééducation pour les personnes handicapées physiques.'
    }
  ];

  cardsPerView = 4;
  hoveredCardIndex: number | null = null;
  translateX = 0;
  cardWidth = 295;
  Math = Math;

  get dots(): number[] {
    const totalSlides = Math.ceil(this.evenements.length / this.cardsPerView);
    return Array(totalSlides).fill(0).map((_, i) => i);
  }

  ngOnInit() {
    this.updateResponsiveSettings();
    window.addEventListener('resize', () => this.updateResponsiveSettings());
  }

  updateResponsiveSettings() {
    this.updateCardsPerView();
    this.updateTranslateX();
  }

  updateCardsPerView() {
    const width = window.innerWidth;
    if (width <= 576) {
      this.cardsPerView = 1;
      this.cardWidth = 280;
    } else if (width <= 768) {
      this.cardsPerView = 2;
      this.cardWidth = 250;
    } else if (width <= 1200) {
      this.cardsPerView = 3;
      this.cardWidth = 280;
    } else {
      this.cardsPerView = 4;
      this.cardWidth = 330;
    }
  }

  updateTranslateX() {
    const gap = 24;
    this.translateX = -this.currentIndex * (this.cardWidth + gap);
  }

  nextSlide() {
    const maxIndex = Math.max(0, this.evenements.length - this.cardsPerView);
    if (this.currentIndex < maxIndex) {
      this.currentIndex++;
      this.updateTranslateX();
    }
  }

  previousSlide() {
    if (this.currentIndex > 0) {
      this.currentIndex--;
      this.updateTranslateX();
    }
  }

  goToSlide(slideIndex: number) {
    this.currentIndex = slideIndex * this.cardsPerView;
    const maxIndex = Math.max(0, this.evenements.length - this.cardsPerView);
    if (this.currentIndex >= maxIndex) {
      this.currentIndex = maxIndex;
    }
    this.updateTranslateX();
  }

  onCardHover(index: number, isHovered: boolean) {
    this.hoveredCardIndex = isHovered ? index : null;
  }

  voirPlus() {
    this.router.navigate(['/admin/gestion-evenements']);
  }

  participerEvenement() {
    this.router.navigate(['/beneficiaire/participation']);
  }

  isLastSlide(): boolean {
    const maxPossibleIndex = Math.max(0, this.evenements.length - this.cardsPerView);
    return this.currentIndex >= maxPossibleIndex;
  }
}
