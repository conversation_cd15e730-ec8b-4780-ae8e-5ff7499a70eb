// Variables
$primary-color: #2196F3;
$secondary-color: #FFC107;
$success-color: #4CAF50;
$error-color: #F44336;
$warning-color: #FF9800;
$info-color: #00BCD4;
$light-gray: #f8f9fa;
$border-color: #e0e0e0;
$text-dark: #333;
$text-muted: #6c757d;
$shadow-light: 0 2px 4px rgba(0,0,0,0.1);
$shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
$border-radius: 8px;

// Container principal
.pad {
  min-height: 100vh;
  background: transparent;
  font-family: 'Roboto', sans-serif;
  margin-top: 5rem;
}

// En-tête avec bouton retour
.back-title-container {
  display: flex;
  align-items: center;
  padding: 1rem 0;
  margin-bottom: 1rem;
  
  .back-arrow {
    font-size: 1.5rem;
    color: $primary-color;
    cursor: pointer;
    margin-right: 1rem;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: rgba($primary-color, 0.1);
      transform: translateX(-2px);
    }
  }
  
  .page-title {
    color: $text-dark;
    font-weight: 600;
    margin: 0;
    font-size: 1.75rem;
  }
}

// Carte principale
mat-card {
  border-radius: $border-radius !important;
  box-shadow: $shadow-medium !important;
  border: none !important;
  
  mat-card-header {
    text-align: center;
    
    mat-card-title {
      color: $primary-color;
      font-weight: 600;
      font-size: 1.5rem;
      line-height: 1.4;
    }
    
    mat-card-subtitle {
      color: $text-muted;
      font-size: 1rem;
      margin-top: 0.5rem;
    }
  }
}

// Sections du formulaire
.form-section {
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: 1.5rem;
  margin-bottom: 2rem;
  background-color: #fff;
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: $shadow-light;
  }
  
  .section-title {
    color: $primary-color;
    font-weight: 600;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba($primary-color, 0.1);
    
    .section-icon {
      margin-right: 0.75rem;
      font-size: 1.5rem;
      color: $secondary-color;
    }
  }
}

// Champs de formulaire Material
.mat-mdc-form-field {
  &.w-100 {
    width: 100% !important;
  }
  
  .mat-mdc-form-field-wrapper {
    padding-bottom: 0 !important;
  }
  
  .mdc-text-field--outlined {
    border-radius: $border-radius !important;
  }
  
  .mat-mdc-form-field-hint-wrapper,
  .mat-mdc-form-field-error-wrapper {
    padding: 0 1rem;
  }
  
  .mat-mdc-form-field-error {
    color: $error-color;
    font-size: 0.875rem;
  }
  
  .mat-mdc-form-field-hint {
    color: $text-muted;
    font-size: 0.875rem;
  }
}

// Labels personnalisés
.form-label {
  color: $text-dark;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  
  &.fw-bold {
    font-weight: 700;
  }
}

// Groupes de boutons radio
mat-radio-group {
  .mat-mdc-radio-button {
    margin-right: 1.5rem;
    
    .mdc-radio {
      .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__outer-circle {
        border-color: $primary-color;
      }
      
      .mdc-radio__background::before {
        background-color: $primary-color;
      }
    }
    
    .mdc-form-field > label {
      color: $text-dark;
      font-weight: 500;
    }
  }
}

// Checkboxes
mat-checkbox {
  .mat-mdc-checkbox {
    .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
      background-color: $primary-color;
      border-color: $primary-color;
    }
  }
  
  .mdc-form-field > label {
    color: $text-dark;
    font-weight: 500;
  }
}

// Section de téléversement
.upload-section {
  .upload-item {
    border: 2px dashed $border-color;
    border-radius: $border-radius;
    padding: 1rem;
    transition: all 0.3s ease;
    background-color: #fafafa;
    
    &:hover {
      border-color: $primary-color;
      background-color: rgba($primary-color, 0.02);
    }
    
    .upload-btn {
      border: none !important;
      background-color: transparent !important;
      color: $primary-color !important;
      font-weight: 500 !important;
      padding: 0.75rem 1rem !important;
      
      mat-icon {
        margin-right: 0.5rem;
        font-size: 1.25rem;
      }
      
      &:hover {
        background-color: rgba($primary-color, 0.05) !important;
      }
    }
    
    .file-info {
      background-color: rgba($success-color, 0.1);
      border-radius: 4px;
      padding: 0.5rem;
      margin-top: 0.5rem;
      
      small {
        display: flex;
        align-items: center;
        color: $success-color;
        font-weight: 500;
        
        .small-icon {
          font-size: 1rem;
          margin-right: 0.25rem;
        }
      }
      
      .small-btn {
        width: 32px !important;
        height: 32px !important;
        line-height: 32px !important;
        
        mat-icon {
          font-size: 1rem;
        }
      }
    }
  }
}

// Section des conditions
.conditions-section {
  background-color: $light-gray;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  
  mat-checkbox {
    display: block;
    margin-bottom: 0.75rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Bouton de soumission
.submit-btn {
  background-color: $primary-color !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 0.75rem 2rem !important;
  border-radius: $border-radius !important;
  font-size: 1rem !important;
  min-width: 200px;
  transition: all 0.3s ease !important;
  
  mat-icon {
    margin-right: 0.5rem;
    font-size: 1.25rem;
  }
  
  &:hover:not(:disabled) {
    background-color: darken($primary-color, 10%) !important;
    transform: translateY(-1px);
    box-shadow: $shadow-medium;
  }
  
  &:disabled {
    background-color: #ccc !important;
    color: #999 !important;
    cursor: not-allowed;
  }
}

// Messages d'erreur globaux
mat-error {
  color: $error-color !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem;
  display: block;
}

// Responsive
@media (max-width: 768px) {
  .back-title-container {
    .page-title {
      font-size: 1.5rem;
    }
  }
  
  .form-section {
    padding: 1rem;
    
    .section-title {
      font-size: 1.1rem;
      
      .section-icon {
        font-size: 1.25rem;
      }
    }
  }
  
  mat-card {
    margin: 0.5rem;
  }
  
  .submit-btn {
    width: 100% !important;
    min-width: auto !important;
  }
}

@media (max-width: 576px) {
  .pad {
    padding: 0.5rem;
  }
  
  .back-title-container {
    padding: 0.5rem 0;
    
    .page-title {
      font-size: 1.25rem;
    }
  }
  
  .form-section {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }
  
  mat-card-header {
    mat-card-title {
      font-size: 1.25rem !important;
    }
    
    mat-card-subtitle {
      font-size: 0.9rem !important;
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-section {
  animation: fadeInUp 0.6s ease-out;
  
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }
  &:nth-child(6) { animation-delay: 0.6s; }
}

// Styles pour les snackbars
.success-snackbar {
  background-color: $success-color !important;
  color: white !important;
}

.error-snackbar {
  background-color: $error-color !important;
  color: white !important;
}

// Focus states améliorés
.mat-mdc-form-field.mat-focused {
  .mdc-notched-outline__leading,
  .mdc-notched-outline__notch,
  .mdc-notched-outline__trailing {
    border-color: $primary-color !important;
    border-width: 2px !important;
  }
}

// Amélioration des sélecteurs
.mat-mdc-select-panel {
  border-radius: $border-radius !important;
  
  .mat-mdc-option {
    &:hover {
      background-color: rgba($primary-color, 0.1) !important;
    }
    
    &.mdc-list-item--selected {
      background-color: rgba($primary-color, 0.15) !important;
    }
  }
}