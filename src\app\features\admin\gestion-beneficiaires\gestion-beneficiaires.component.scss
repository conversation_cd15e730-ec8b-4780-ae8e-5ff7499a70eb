.back-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.5rem;
  padding: 0 2rem;
  margin-top: 6.5rem;
}

.back-arrow {
  font-size: 1.5rem;
  cursor: pointer;
  color: #3498db;
  transition: color 0.3s;

  &:hover {
    color: #2ecc71;
  }
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.table {
  th {
    font-weight: 600;
    color: black;
    border-bottom: 2px solid #dee2e6;
    text-align: center;
  }
  td {
    text-align: center;
  }
  thead th{
      background-color: #2ecc71;
  }

  tbody tr:hover {
    background-color: #f8f9fa;
  }
}

.btn-outline-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  // margin-top: 5rem;
  margin-bottom: 5rem;
  margin-left: 3rem;
  margin-right: 3rem;
}
.color{
  color: green;
}

.badge {
  font-size: 0.75rem;
  padding: 0.5em 0.75em;
  border-radius: 6px;
  color: #2c3e50;
}

h2 {
  color: #2c3e50;
  font-weight: 600;
}

// Responsive adjustments
@media (max-width: 768px) {
  .navbar-nav {
    flex-direction: column !important;
    
    .nav-item {
      margin-right: 0 !important;
      margin-bottom: 0.5rem;
    }
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  h2 {
    font-size: 1.5rem;
  }
}

// Animation for table rows
tbody tr {
  transition: background-color 0.2s ease-in-out;
}

// Custom scrollbar for table
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.bi-person-circle{
    margin-top: 0.60rem;
}