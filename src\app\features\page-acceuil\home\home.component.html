    <section class="welcome-section">
      <div class="container-fluid h-100">
        <div class="row h-100">
          <div class="col-12 position-relative">
            <!-- Image de fond -->
            <div class="background-image"></div>
            
            <!-- Overlay pour améliorer la lisibilité -->
            <div class="overlay"></div>
            
            <!-- Contenu par-dessus l'image -->
            <div class="content-overlay">
              <div class="text-content">
                <h1 class="welcome-title">Soyez les bienvenus</h1>
                
                <!-- Barre de recherche -->
                <div class="search-container">
                  <mat-form-field appearance="fill" class="search-field">
                    <mat-icon matPrefix class="search-icon">search</mat-icon>
                    <input 
                      matInput 
                      [(ngModel)]="searchQuery"
                      placeholder="Recherche"
                      class="search-input"
                      (keyup.enter)="onSearch()">
                  </mat-form-field>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section><br>
    <!-- --------------------------------------------------------------------------- -->
         <div class="services-section py-5">
      <div class="container">
        <!-- Titre de la section -->
        <div class="text-center mb-5">
          <h2 class="services-title">Nos services</h2>
        </div>

        <!-- Navigation et cartes -->
        <div class="services-carousel position-relative">
          <!-- Bouton précédent -->
          <button 
            mat-icon-button 
            class="nav-btn nav-prev"
            (click)="previousPage()"
            [disabled]="currentIndex === 0">
            <mat-icon>chevron_left</mat-icon>
          </button>

          <!-- Cartes de services -->
          <div class="row justify-content-center">
            <div 
              class="col-md-4 mb-4" 
              *ngFor="let service of getCurrentServices()">
              <div class="service-card">
                <div class="service-image-container">
                  <img 
                    [src]="service.image" 
                    [alt]="service.title"
                    class="service-image">
                </div>
                <div class="service-content">
                  <h5 class="service-title">{{ service.title }}</h5>
                  <button 
                    mat-raised-button 
                    class="service-btn"
                    (click)="makeRequest()">
                    Faire une demande
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Bouton suivant -->
          <button 
            mat-icon-button 
            class="nav-btn nav-next"
            (click)="nextPage()"
            [disabled]="currentIndex >= totalPages - 1">
            <mat-icon>chevron_right</mat-icon>
          </button>
        </div>

        <!-- Indicateurs de pagination -->
        <div class="pagination-indicators text-center mt-4">
          <span 
            class="indicator"
            *ngFor="let page of getPageIndicators(); let i = index"
            [class.active]="i === currentIndex"
            (click)="goToPage(i)">
          </span>
        </div>
      </div>
    </div>
    <!-- --------------------------------------------------------------------------- -->

        <div class="evenements-container">       
  <h1 class="text-center mb-5 evenements-title">Événements</h1>              
  
  <div class="carousel-container position-relative">         
    <!-- Flèche gauche -->         
    <button            
      mat-fab            
      color="primary"            
      class="carousel-arrow arrow-left"           
      (click)="previousSlide()"           
      [disabled]="currentIndex === 0">           
      <mat-icon>keyboard_arrow_left</mat-icon>         
    </button>          

    <!-- Conteneur des cartes -->         
    <div class="cards-wrapper">           
      <div              
        class="cards-container"             
        [style.transform]="'translateX(' + translateX + 'px)'">                          
        
        <div                
          *ngFor="let evenement of evenements; let i = index"               
          class="card-item"               
          [class.active]="i >= currentIndex && i < currentIndex + cardsPerView">                              
          
          <mat-card                  
            class="evenement-card h-100"                 
            (mouseenter)="onCardHover(i, true)"                 
            (mouseleave)="onCardHover(i, false)">                                  
            
            <div class="card-image-container position-relative">                   
              <img                      
                [src]="evenement.image"                      
                [alt]="evenement.titre"                     
                class="card-img-top">                                      

              <!-- Overlay avec boutons -->                   
              <div                      
                class="card-overlay"                     
                [class.show]="hoveredCardIndex === i">                     
                
                <div class="overlay-buttons">
                  <button                        
                    mat-raised-button                        
                    color="primary"                       
                    class="voir-plus-btn"                       
                    (click)="voirPlus()">                       
                    <mat-icon>visibility</mat-icon>                       
                    Voir plus                     
                  </button>
                  
                  <button                        
                    mat-raised-button                        
                    color="accent"                       
                    class="participer-btn"                       
                    (click)="participerEvenement()">                       
                    <mat-icon>event_available</mat-icon>                       
                    Participer à l'événement                     
                  </button>
                </div>
              </div>                 
            </div>                  

            <mat-card-content class="card-content">                   
              <h5 class="card-title text-white">{{ evenement.titre }}</h5>                   
              <p class="card-beneficiaires text-white">{{ evenement.beneficiaires }} bénéficiaires</p>                 
            </mat-card-content>               
          </mat-card>             
        </div>           
      </div>         
    </div>          

    <!-- Flèche droite -->         
    <button            
      mat-fab            
      color="primary"            
      class="carousel-arrow arrow-right"           
      (click)="nextSlide()"           
      [disabled]="isLastSlide()">           
      <mat-icon>keyboard_arrow_right</mat-icon>         
    </button>       
  </div>        

  <!-- Indicateurs -->       
  <div class="carousel-indicators text-center mt-4">         
    <button            
      *ngFor="let dot of dots; let i = index"           
      class="indicator-dot"           
      [class.active]="i === Math.floor(currentIndex / cardsPerView)"           
      (click)="goToSlide(i)">         
    </button>       
  </div>     
</div>