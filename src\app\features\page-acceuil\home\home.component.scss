    .welcome-section {
      height: 1000px; /* Ajustez selon vos besoins pour 1/4 de page */
      position: relative;
      overflow: hidden;
      margin-top: 6rem;
    }

    .background-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('/assets/images/background.jpeg'); /* Remplacez par le chemin de votre image */
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }

    .overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3); /* Overlay sombre pour améliorer la lisibilité du texte */
      z-index: 1;
    }

    .content-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;
    }

    .text-content {
      text-align: center;
      max-width: 600px;
      padding: 0 20px;
    }

    .welcome-title {
      font-size: 3rem;
      font-weight: 700;
      color: white;
      margin-bottom: 2rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      line-height: 1.2;
    }

.search-container {
  width: 100%;
  max-width: 500px;
  margin: 20px auto;
  padding: 10px;
  z-index: 10;
}

.search-field {
  width: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  ::ng-deep .mat-mdc-form-field-flex {
    border-radius: 50px;
    padding: 10px 20px;
    background: transparent;
  }

  ::ng-deep .mat-mdc-text-field-wrapper {
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.6);
  }

  ::ng-deep .mat-mdc-form-field-outline {
    display: none; // Supprimer les bordures par défaut
  }

  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
}

.search-icon {
  color: #555;
  font-size: 22px;
}

.search-input {
  font-size: 1rem;
  color: #333;
  font-weight: 500;

  &::placeholder {
    color: #aaa;
    font-weight: 400;
  }
}


    /* Responsive */
    @media (max-width: 768px) {
      .welcome-section {
        height: 300px;
      }
      
      .welcome-title {
        font-size: 2.2rem;
        margin-bottom: 1.5rem;
      }
      
      .search-field ::ng-deep .mat-mdc-form-field-flex {
        padding: 10px 20px;
      }
    }

    @media (max-width: 576px) {
      .welcome-section {
        height: 250px;
      }
      
      .welcome-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
      }
      
      .text-content {
        padding: 0 15px;
      }
    }

    /* Si vous préférez positionner le texte à gauche comme dans l'image originale */
    // @media (min-width: 992px) {
    //   .content-overlay {
    //     justify-content: flex-start;
    //     padding-left: 5%;
    //   }
      
    //   .text-content {
    //     text-align: left;
    //     max-width: 500px;
    //   }
    // }

        .services-section {
      background-color: transparent;
      min-height: 50vh;
    }

    .services-title {
      font-size: 2.5rem;
      font-weight: bold;
      color: #28a745;
      margin-bottom: 3rem;
    }

    .services-carousel {
      position: relative;
      padding: 0 60px;
    }

    .nav-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      background-color: #007bff;
      color: white;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }

    .nav-btn:hover:not([disabled]) {
      background-color: #0056b3;
      transform: translateY(-50%) scale(1.1);
    }

    .nav-btn[disabled] {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .nav-prev {
      left: 0;
    }

    .nav-next {
      right: 0;
    }

    .service-card {
      background: white;
      border-radius: 15px;
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
      overflow: hidden;
      transition: all 0.3s ease;
      height: 100%;
    }

    .service-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .service-image-container {
      height: 200px;
      overflow: hidden;
      background-color: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .service-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      transition: transform 0.3s ease;
    }

    .service-card:hover .service-image {
      transform: scale(1.05);
    }

    .service-content {
      padding: 25px;
      text-align: center;
    }

    .service-title {
      font-size: 1.3rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      min-height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .service-btn {
      background: linear-gradient(45deg, #17a2b8, #20c997);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      font-weight: 500;
      text-transform: none;
      transition: all 0.3s ease;
    }

    .service-btn:hover {
      background: linear-gradient(45deg, #138496, #1c7d6b);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(23, 162, 184, 0.3);
    }

    .pagination-indicators {
      display: flex;
      justify-content: center;
      gap: 10px;
    }

    .indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #dee2e6;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-block;
    }

    .indicator.active {
      background-color: #007bff;
      transform: scale(1.2);
    }

    .indicator:hover {
      background-color: #6c757d;
    }

    @media (max-width: 768px) {
      .services-carousel {
        padding: 0 20px;
      }
      
      .nav-btn {
        width: 40px;
        height: 40px;
      }
      
      .services-title {
        font-size: 2rem;
      }
      
      .service-image-container {
        height: 150px;
      }
    }

    @media (max-width: 576px) {
      .services-carousel {
        padding: 0;
      }
      
      .nav-btn {
        display: none;
      }
    }
    // ------------------------------------------------------------------------------


     .evenements-container {
  padding: 2rem;
  // background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  background: transparent;
  min-height: 100vh;
}

.evenements-title {
  color: #4CAF50;
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 3rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.carousel-container {
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
}

.cards-wrapper {
  overflow: hidden;
  margin: 0 80px;
}

.cards-container {
  display: flex;
  transition: transform 0.5s ease-in-out;
  gap: 1.5rem;
}

.card-item {
  flex: 0 0 calc(25% - 1.125rem);
  min-width: 280px;
}

.evenement-card {
  background: linear-gradient(145deg, rgba(76, 175, 80, 0.9), rgba(56, 142, 60, 0.9));
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  cursor: pointer;
}

.evenement-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0,0,0,0.25);
}

.card-image-container {
  height: 200px;
  position: relative;
  overflow: hidden;
}

.card-img-top {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.evenement-card:hover .card-img-top {
  transform: scale(1.1);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  padding: 1rem;
}

.card-overlay.show {
  opacity: 1;
}

.overlay-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  align-items: center;
}

.voir-plus-btn,
.participer-btn {
  transform: translateY(20px);
  transition: transform 0.3s ease;
  white-space: nowrap;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

.card-overlay.show .voir-plus-btn,
.card-overlay.show .participer-btn {
  transform: translateY(0);
}

.participer-btn {
  transition-delay: 0.1s;
}

.card-content {
  padding: 1.5rem !important;
  text-align: center;
}

.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.card-beneficiaires {
  font-size: 1rem;
  margin-bottom: 0;
  opacity: 0.9;
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  transition: all 0.3s ease;
}

.carousel-arrow:hover:not(:disabled) {
  transform: translateY(-50%) scale(1.1);
}

.carousel-arrow:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.arrow-left {
  left: 10px;
}

.arrow-right {
  right: 10px;
}

.carousel-indicators {
  margin-top: 2rem;
}

.indicator-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(76, 175, 80, 0.3);
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background: #4CAF50;
  transform: scale(1.2);
}

.indicator-dot:hover {
  background: #4CAF50;
  transform: scale(1.1);
}

/* Responsive */
@media (max-width: 1200px) {
  .card-item {
    flex: 0 0 calc(33.333% - 1rem);
  }
}

@media (max-width: 768px) {
  .cards-wrapper {
    margin: 0 60px;
  }
  
  .card-item {
    flex: 0 0 calc(50% - 0.75rem);
    min-width: 250px;
  }
  
  .evenements-title {
    font-size: 2rem;
  }
  
  .overlay-buttons {
    gap: 0.6rem;
  }
  
  .voir-plus-btn,
  .participer-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

@media (max-width: 576px) {
  .cards-wrapper {
    margin: 0 50px;
  }
  
  .card-item {
    flex: 0 0 100%;
    min-width: 280px;
  }
  
  .evenements-container {
    padding: 1rem;
  }
  
  .overlay-buttons {
    gap: 0.5rem;
  }
  
  .voir-plus-btn,
  .participer-btn {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
  }
}